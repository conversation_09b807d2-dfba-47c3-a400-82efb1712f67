{"name": "@roo-code/vscode-e2e", "private": true, "scripts": {"lint": "eslint src --ext=ts --max-warnings=0", "check-types": "tsc -p tsconfig.esm.json --noEmit", "format": "prettier --write src", "test:ci": "pnpm -w bundle && pnpm --filter @roo-code/vscode-webview build && pnpm test:run", "test:run": "rimraf out && tsc -p tsconfig.json && npx dotenvx run -f .env.local -- node ./out/runTest.js", "clean": "rimraf out .turbo"}, "devDependencies": {"@roo-code/config-eslint": "workspace:^", "@roo-code/config-typescript": "workspace:^", "@roo-code/types": "workspace:^", "@types/mocha": "^10.0.10", "@types/node": "^22.14.1", "@types/vscode": "^1.95.0", "@vscode/test-cli": "^0.0.11", "@vscode/test-electron": "^2.4.0", "glob": "^11.0.1", "mocha": "^11.1.0", "rimraf": "^6.0.1", "typescript": "5.8.3"}}