# Traditional Chinese (zh-TW) Translation Guidelines

## Key Terminology

| English Term  | Use (zh-TW) | Avoid (Mainland) |
| ------------- | ----------- | ---------------- |
| file          | 檔案        | 文件             |
| task          | 工作        | 任務             |
| project       | 專案        | 項目             |
| configuration | 設定        | 配置             |
| server        | 伺服器      | 服務器           |
| import/export | 匯入/匯出   | 導入/導出        |

## Formatting Rules

- Add spaces between Chinese and English/numbers: "AI 驅動" (not "AI驅動")
- Use Traditional Chinese quotation marks: 「範例文字」(not "範例文字")
- Use Taiwanese computing conventions rather than mainland terminology
