import { useCallback } from "react"

import { useExtensionState } from "@/context/ExtensionStateContext"
import { vscode } from "@/utils/vscode"

interface HeaderProps {
	currentTab: string
	onTabChange: (tab: string) => void
}

export const Header = ({ currentTab, onTabChange }: HeaderProps) => {
	const { version } = useExtensionState()

	const handleNewTask = useCallback(() => {
		vscode.postMessage({ type: "newTask" })
		onTabChange("chat")
	}, [onTabChange])

	const navigationButtons = [
		{
			id: "new-task",
			icon: "add",
			title: "New Task",
			onClick: handleNewTask,
			isActive: false,
		},
		{
			id: "chat",
			icon: "comment-discussion",
			title: "Chat",
			onClick: () => onTabChange("chat"),
			isActive: currentTab === "chat",
		},
		{
			id: "modes",
			icon: "layers",
			title: "Modes",
			onClick: () => onTabChange("modes"),
			isActive: currentTab === "modes",
		},
		{
			id: "mcp",
			icon: "server-process",
			title: "MCP Servers",
			onClick: () => onTabChange("mcp"),
			isActive: currentTab === "mcp",
		},
		{
			id: "history",
			icon: "history",
			title: "History",
			onClick: () => onTabChange("history"),
			isActive: currentTab === "history",
		},
		{
			id: "account",
			icon: "account",
			title: "Account",
			onClick: () => onTabChange("account"),
			isActive: currentTab === "account",
		},
		{
			id: "settings",
			icon: "settings-gear",
			title: "Settings",
			onClick: () => onTabChange("settings"),
			isActive: currentTab === "settings",
		},
	]

	return (
		<header
			className="flex items-center justify-between px-4 py-3 border-b"
			style={{
				backgroundColor: "var(--vscode-sideBar-background)",
				borderBottomColor: "var(--vscode-panel-border)",
				minHeight: "48px",
				boxShadow: "0 1px 3px rgba(0,0,0,0.1)"
			}}
		>
			{/* Logo/Title */}
			<div className="flex items-center gap-3">
				<h1
					className="text-lg font-bold"
					style={{ color: "var(--vscode-foreground)" }}
				>
					ROO CODE
				</h1>
				{version && (
					<span
						className="text-sm px-2 py-1 rounded"
						style={{
							color: "var(--vscode-descriptionForeground)",
							backgroundColor: "var(--vscode-badge-background)",
							fontSize: "11px"
						}}
					>
						{version.replace("dev-mode-", "")}
					</span>
				)}
			</div>

			{/* Navigation Buttons */}
			<div className="flex items-center gap-1">
				{navigationButtons.map(({ id, icon, title, onClick, isActive }) => (
					<button
						key={id}
						title={title}
						onClick={onClick}
						className={`
							flex flex-col items-center justify-center rounded-md transition-all duration-200 cursor-pointer
							border border-transparent relative
							${isActive ? "shadow-md" : "hover:shadow-sm"}
						`}
						style={{
							minWidth: "60px",
							height: "40px",
							padding: "4px 8px",
							backgroundColor: isActive
								? "var(--vscode-button-background)"
								: "transparent",
							color: isActive
								? "var(--vscode-button-foreground)"
								: "var(--vscode-foreground)",
							borderColor: isActive
								? "var(--vscode-focusBorder)"
								: "transparent"
						}}
						onMouseEnter={(e) => {
							if (!isActive) {
								e.currentTarget.style.backgroundColor = "var(--vscode-toolbar-hoverBackground)"
								e.currentTarget.style.borderColor = "var(--vscode-focusBorder)"
								e.currentTarget.style.transform = "translateY(-1px)"
							}
						}}
						onMouseLeave={(e) => {
							if (!isActive) {
								e.currentTarget.style.backgroundColor = "transparent"
								e.currentTarget.style.borderColor = "transparent"
								e.currentTarget.style.transform = "translateY(0)"
							}
						}}
						data-testid={`header-${id}-button`}
					>
						<span
							className={`codicon codicon-${icon}`}
							style={{
								fontSize: "16px",
								fontWeight: isActive ? "bold" : "normal",
								marginBottom: "2px"
							}}
						/>
						<span
							style={{
								fontSize: "9px",
								fontWeight: "500",
								textTransform: "uppercase",
								letterSpacing: "0.5px",
								opacity: isActive ? 1 : 0.8
							}}
						>
							{title.split(' ')[0]}
						</span>
						{isActive && (
							<div
								style={{
									position: "absolute",
									bottom: "-1px",
									left: "50%",
									transform: "translateX(-50%)",
									width: "80%",
									height: "2px",
									backgroundColor: "var(--vscode-button-background)",
									borderRadius: "1px"
								}}
							/>
						)}
					</button>
				))}
			</div>
		</header>
	)
}

export default Header
