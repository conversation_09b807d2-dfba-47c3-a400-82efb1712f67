import { useCallback } from "react"

import { useExtensionState } from "@/context/ExtensionStateContext"
import { vscode } from "@/utils/vscode"

interface HeaderProps {
	currentTab: string
	onTabChange: (tab: string) => void
}

export const Header = ({ currentTab, onTabChange }: HeaderProps) => {
	const { version } = useExtensionState()

	const handleNewTask = useCallback(() => {
		vscode.postMessage({ type: "newTask" })
		onTabChange("chat")
	}, [onTabChange])

	const navigationButtons = [
		{
			id: "new-task",
			icon: "add",
			title: "New Task",
			onClick: handleNewTask,
			isActive: false,
		},
		{
			id: "chat",
			icon: "comment-discussion",
			title: "Chat",
			onClick: () => onTabChange("chat"),
			isActive: currentTab === "chat",
		},
		{
			id: "modes",
			icon: "layers",
			title: "Modes",
			onClick: () => onTabChange("modes"),
			isActive: currentTab === "modes",
		},
		{
			id: "mcp",
			icon: "server-process",
			title: "MCP Servers",
			onClick: () => onTabChange("mcp"),
			isActive: currentTab === "mcp",
		},
		{
			id: "history",
			icon: "history",
			title: "History",
			onClick: () => onTabChange("history"),
			isActive: currentTab === "history",
		},
		{
			id: "account",
			icon: "account",
			title: "Account",
			onClick: () => onTabChange("account"),
			isActive: currentTab === "account",
		},
		{
			id: "settings",
			icon: "settings-gear",
			title: "Settings",
			onClick: () => onTabChange("settings"),
			isActive: currentTab === "settings",
		},
	]

	return (
		<header
			className="flex items-center justify-between px-3 py-2 border-b"
			style={{
				backgroundColor: "var(--vscode-sideBar-background)",
				borderBottomColor: "var(--vscode-panel-border)",
				minHeight: "35px"
			}}
		>
			{/* Logo/Title */}
			<div className="flex items-center gap-2">
				<h1
					className="text-sm font-semibold"
					style={{ color: "var(--vscode-foreground)" }}
				>
					ROO CODE
				</h1>
				{version && (
					<span
						className="text-xs opacity-70"
						style={{ color: "var(--vscode-descriptionForeground)" }}
					>
						{version.replace("dev-mode-", "")}
					</span>
				)}
			</div>

			{/* Navigation Buttons */}
			<div className="flex items-center gap-1">
				{navigationButtons.map(({ id, icon, title, onClick, isActive }) => (
					<button
						key={id}
						title={title}
						onClick={onClick}
						className={`
							w-7 h-7 flex items-center justify-center rounded-sm transition-colors cursor-pointer
							${isActive ? "opacity-100" : "opacity-70 hover:opacity-100"}
						`}
						style={{
							backgroundColor: isActive
								? "var(--vscode-button-background)"
								: "transparent",
							color: isActive
								? "var(--vscode-button-foreground)"
								: "var(--vscode-foreground)",
						}}
						onMouseEnter={(e) => {
							if (!isActive) {
								e.currentTarget.style.backgroundColor = "var(--vscode-toolbar-hoverBackground)"
							}
						}}
						onMouseLeave={(e) => {
							if (!isActive) {
								e.currentTarget.style.backgroundColor = "transparent"
							}
						}}
						data-testid={`header-${id}-button`}
					>
						<span className={`codicon codicon-${icon}`} style={{ fontSize: "16px" }} />
					</button>
				))}
			</div>
		</header>
	)
}

export default Header
