import { useCallback } from "react"
import { Plus, Settings, History, Layers, Server, User } from "lucide-react"

import { useExtensionState } from "@/context/ExtensionStateContext"
import { vscode } from "@/utils/vscode"
import { Button } from "@/components/ui"

interface HeaderProps {
	currentTab: string
	onTabChange: (tab: string) => void
}

export const Header = ({ currentTab, onTabChange }: HeaderProps) => {
	const { version } = useExtensionState()

	const handleNewTask = useCallback(() => {
		vscode.postMessage({ type: "newTask" })
		onTabChange("chat")
	}, [onTabChange])

	const navigationButtons = [
		{
			id: "new-task",
			icon: Plus,
			title: "New Task",
			onClick: handleNewTask,
			isActive: false,
		},
		{
			id: "modes",
			icon: Layers,
			title: "Modes",
			onClick: () => onTabChange("modes"),
			isActive: currentTab === "modes",
		},
		{
			id: "mcp",
			icon: Server,
			title: "MCP Servers",
			onClick: () => onTabChange("mcp"),
			isActive: currentTab === "mcp",
		},
		{
			id: "history",
			icon: History,
			title: "History",
			onClick: () => onTabChange("history"),
			isActive: currentTab === "history",
		},
		{
			id: "account",
			icon: User,
			title: "Account",
			onClick: () => onTabChange("account"),
			isActive: currentTab === "account",
		},
		{
			id: "settings",
			icon: Settings,
			title: "Settings",
			onClick: () => onTabChange("settings"),
			isActive: currentTab === "settings",
		},
	]

	return (
		<header className="flex items-center justify-between px-4 py-2 border-b border-vscode-panel-border bg-vscode-sideBar-background">
			{/* Logo/Title */}
			<div className="flex items-center gap-2">
				<h1 className="text-sm font-semibold text-vscode-foreground">ROO CODE</h1>
				{version && (
					<span className="text-xs text-vscode-descriptionForeground opacity-70">
						{version.replace("dev-mode-", "")}
					</span>
				)}
			</div>

			{/* Navigation Buttons */}
			<div className="flex items-center gap-1">
				{navigationButtons.map(({ id, icon: Icon, title, onClick, isActive }) => (
					<Button
						key={id}
						variant="ghost"
						size="icon"
						title={title}
						onClick={onClick}
						className={`
							w-8 h-8 p-1.5 rounded-md transition-colors
							${
								isActive
									? "bg-vscode-button-background text-vscode-button-foreground"
									: "text-vscode-foreground hover:bg-vscode-button-hoverBackground"
							}
						`}
						data-testid={`header-${id}-button`}
					>
						<Icon className="w-4 h-4" />
					</Button>
				))}
			</div>
		</header>
	)
}

export default Header
