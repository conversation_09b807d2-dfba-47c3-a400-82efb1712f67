[English](../../CONTRIBUTING.md) • [<PERSON><PERSON><PERSON>](../ca/CONTRIBUTING.md) • [<PERSON><PERSON><PERSON>](../de/CONTRIBUTING.md) • [<PERSON><PERSON><PERSON><PERSON><PERSON>](../es/CONTRIBUTING.md) • [Français](../fr/CONTRIBUTING.md) • [हिंदी](../hi/CONTRIBUTING.md) • [Italiano](../it/CONTRIBUTING.md) • [Nederlands](../nl/CONTRIBUTING.md) • <b>Русский</b>

[日本語](../ja/CONTRIBUTING.md) • [한국어](../ko/CONTRIBUTING.md) • [Polski](../pl/CONTRIBUTING.md) • [Português (BR)](../pt-BR/CONTRIBUTING.md) • [Türkçe](../tr/CONTRIBUTING.md) • [Tiếng Việt](../vi/CONTRIBUTING.md) • [简体中文](../zh-CN/CONTRIBUTING.md) • [繁體中文](../zh-TW/CONTRIBUTING.md)

# Вклад в Roo Code

Roo Code — проект, управляемый сообществом, и мы высоко ценим каждый вклад. Для упрощения сотрудничества мы работаем по принципу [Issue-First](#подход-issue-first), что означает, что все [Pull Request (PR)](#отправка-pull-request) должны сначала быть связаны с GitHub Issue. Пожалуйста, внимательно ознакомься с этим руководством.

## Содержание

- [Перед тем как внести вклад](#перед-тем-как-внести-вклад)
- [Поиск и планирование вклада](#поиск-и-планирование-вклада)
- [Процесс разработки и отправки](#процесс-разработки-и-отправки)
- [Юридическая информация](#юридическая-информация)

## Перед тем как внести вклад

### 1. Кодекс поведения

Все участники должны соблюдать наш [Кодекс поведения](./CODE_OF_CONDUCT.md).

### 2. Дорожная карта проекта

Наша дорожная карта определяет направление проекта. Согласуй свой вклад с этими ключевыми целями:

### Надежность в первую очередь

- Обеспечение стабильной работы редактирования различий и выполнения команд
- Сокращение точек трения, препятствующих регулярному использованию
- Гарантия бесперебойной работы на всех языках и платформах
- Расширение надежной поддержки для широкого спектра ИИ-провайдеров и моделей

### Улучшенный пользовательский опыт

- Упрощение пользовательского интерфейса для большей ясности и интуитивности
- Постоянное совершенствование рабочего процесса для соответствия высоким ожиданиям разработчиков

### Лидерство в производительности агентов

- Создание комплексных показателей оценки (evals) для измерения реальной продуктивности
- Упрощение запуска и интерпретации этих оценок для всех пользователей
- Внедрение улучшений, демонстрирующих явное повышение оценочных показателей

Упоминай связь с этими направлениями в своих PR.

### 3. Присоединяйся к сообществу Roo Code

- **Основной способ:** Присоединись к нашему [Discord](https://discord.gg/roocode) и отправь личное сообщение **Hannes Rudolph (`hrudolph`)**.
- **Альтернатива:** Опытные участники могут взаимодействовать напрямую через [GitHub Projects](https://github.com/orgs/RooCodeInc/projects/1).

## Поиск и планирование вклада

### Виды вклада

- **Исправление ошибок:** Решение проблем в коде.
- **Новые функции:** Добавление функциональности.
- **Документация:** Улучшение руководств и ясности.

### Подход Issue-First

Весь вклад должен начинаться с GitHub Issue.

- **Проверь существующие issues:** Поищи в [GitHub Issues](https://github.com/RooCodeInc/Roo-Code/issues).
- **Создай issue:** Используй подходящие шаблоны:
    - **Баги:** Шаблон "Bug Report".
    - **Функции:** Шаблон "Detailed Feature Proposal". Требуется одобрение перед началом.
- **Заяви issue:** Оставь комментарий и дождись официального назначения.

**PR без одобренных issue могут быть закрыты.**

### Решение, над чем работать

- Проверь [GitHub проект](https://github.com/orgs/RooCodeInc/projects/1) на наличие незанятых "Good First Issues".
- Для документации посети [Roo Code Docs](https://github.com/RooCodeInc/Roo-Code-Docs).

### Сообщение об ошибках

- Сначала проверь существующие сообщения.
- Создай новые сообщения об ошибках, используя [шаблон "Bug Report"](https://github.com/RooCodeInc/Roo-Code/issues/new/choose).
- **Уязвимости безопасности:** Сообщай приватно через [security advisories](https://github.com/RooCodeInc/Roo-Code/security/advisories/new).

## Процесс разработки и отправки

### Настройка среды разработки

1. **Fork & Clone:**

```
git clone https://github.com/ТВОЙ_ПОЛЬЗОВАТЕЛЬ/Roo-Code.git
```

2. **Установка зависимостей:**

```
npm run install:all
```

3. **Отладка:** Открой в VS Code (`F5`).

### Руководство по написанию кода

- Один сфокусированный PR на функцию или исправление.
- Следуй лучшим практикам ESLint и TypeScript.
- Пиши ясные, описательные сообщения коммитов с ссылками на issues (например, `Fixes #123`).
- Обеспечь тщательное тестирование (`npm test`).
- Перебазируй на последнюю ветку `main` перед отправкой.

### Отправка Pull Request

- Начни с **черновика PR**, если ищешь ранний фидбек.
- Четко опиши свои изменения, следуя шаблону Pull Request.
- Предоставь скриншоты/видео для изменений UI.
- Укажи, нужны ли обновления документации.

### Политика Pull Request

- Должен ссылаться на предварительно одобренные и назначенные issue.
- PR, не соответствующие политике, могут быть закрыты.
- PR должны проходить CI-тесты, соответствовать дорожной карте и иметь четкую документацию.

### Процесс проверки

- **Ежедневный отбор:** Быстрые проверки мейнтейнерами.
- **Еженедельный глубокий обзор:** Комплексная оценка.
- **Быстро итерируй** на основе полученного фидбека.

## Юридическая информация

Отправляя pull request, ты соглашаешься, что твой вклад будет лицензирован под лицензией Apache 2.0, в соответствии с лицензией Roo Code.
